 /* system-settings.wxss - High-end Redesign */

/* Page background and container */
page {
  background-color: #f8f8f8;
}

.container {
  padding: 32rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

/* Modernized Setting Card */
.setting-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  padding: 32rpx;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

.setting-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1);
}

/* Card Header */
.card-header {
  display: flex;
  align-items: center;
  padding-bottom: 24rpx;
  margin-bottom: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-header .t-icon {
  color: #0052d9;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-left: 16rpx;
}

/* Card Body */
.card-body {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* Setting Item Layout */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  min-height: 60rpx;
}

.setting-item:not(:last-child) {
  border-bottom: 1rpx solid #f5f5f5;
}

.setting-label {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

/* Cancel Time Setting Specifics */
.time-setting-value {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.time-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #0052d9;
}

.time-unit {
  font-size: 24rpx;
  color: #666666;
}

.input-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-top: 16rpx;
  background-color: #f8f9fa;
  padding: 16rpx;
  border-radius: 16rpx;
}

.input-label, .input-suffix {
  font-size: 28rpx;
  color: #666;
}

.time-input {
  flex: 1;
  text-align: center;
}

.time-input .t-input__control {
  text-align: center;
  font-weight: 500;
}

/* Contact Info Inputs */
.setting-input {
  flex: 1;
  margin-left: 32rpx;
  text-align: right;
}

.contact-input .t-input__control,
.contact-textarea .t-textarea__control {
  text-align: right;
}

.contact-textarea {
  min-height: 120rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e5e5e5;
}

/* Description Text */
.setting-description {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.5;
  margin-top: 16rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

/* Action Button Section */
.action-section {
  margin-top: 48rpx;
}

.save-btn {
  border-radius: 16rpx !important;
  font-weight: 600 !important;
  height: 96rpx !important;
  font-size: 32rpx !important;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%) !important;
  box-shadow: 0 6rpx 24rpx rgba(24, 144, 255, 0.3) !important;
}

/* Responsive adjustments */
@media (max-width: 750rpx) {
  .container {
    padding: 24rpx;
  }
  
  .setting-card {
    padding: 24rpx;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
    padding: 20rpx 0;
  }
  
  .setting-input {
    margin-left: 0;
    width: 100%;
    text-align: left;
  }

  .contact-input .t-input__control,
  .contact-textarea .t-textarea__control {
    text-align: left;
  }
}
