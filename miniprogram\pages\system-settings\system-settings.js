// system-settings.js
// 系统设置页面逻辑文件
// 这是小程序的系统设置页面，负责管理系统全局配置、联系信息、业务规则等功能
// 类似于Web应用的后台管理页面或移动应用的系统配置页面

/**
 * 模块导入说明
 *
 * 系统设置页面的特点：
 * 1. 权限控制：只有管理员可以访问和修改
 * 2. 全局配置：影响整个小程序的业务逻辑
 * 3. 实时生效：设置修改后立即在全系统生效
 * 4. 数据持久化：所有设置保存到云数据库
 *
 * 安全考虑：
 * - 严格的权限验证
 * - 数据验证和格式化
 * - 操作日志记录
 * - 错误处理和回滚
 */

// 导入Toast工具函数，用于操作反馈
import { showToast, showLoading, hideToast } from '../../utils/toast.js';

/**
 * 获取全局应用实例
 *
 * getApp()函数：
 * - 获取小程序的全局App实例
 * - 可以访问全局数据和方法
 * - 用于权限验证、用户信息获取等
 *
 * 与其他技术对比：
 * - Android: getApplication()
 * - iOS: UIApplication.shared
 * - Web: window对象
 */
const app = getApp()

/**
 * Page()函数：注册系统设置页面
 *
 * 页面功能：
 * 1. 预约规则设置：取消预约时间限制等业务规则
 * 2. 系统状态设置：维护模式开关
 * 3. 联系信息管理：电话、地址、公告等信息
 * 4. 权限控制：确保只有管理员可以操作
 * 5. 数据同步：与云数据库实时同步
 *
 * 设计原则：
 * - 权限优先：所有操作都需要管理员权限
 * - 数据验证：输入数据的格式验证和范围检查
 * - 用户友好：清晰的操作反馈和错误提示
 * - 系统稳定：异常处理和数据回滚机制
 */
Page({
  /**
   * data: 页面数据对象
   *
   * 数据分类：
   * 1. 业务设置：影响系统业务逻辑的配置
   * 2. 系统状态：系统运行状态的控制
   * 3. 信息管理：对外展示的信息内容
   * 4. UI状态：页面交互状态管理
   * 5. 权限控制：用户权限验证结果
   */
  data: {
    /**
     * 预约取消时间设置
     *
     * 业务规则：
     * 用户在课程开始前多长时间内不能取消预约
     * 这个设置影响整个小程序的预约取消逻辑
     *
     * 数据格式：
     * - cancelTimeLimit: 以分钟为单位的数值
     * - formattedTime: 用户友好的显示格式
     * - timeUnit: 时间单位（小时/分钟）
     */

    // 取消预约时间限制（分钟）
    // 默认180分钟（3小时），用户在课程开始前3小时内不能取消预约
    cancelTimeLimit: 180,

    // 格式化显示的时间
    // 将分钟数转换为用户友好的显示格式，如"3"（配合timeUnit显示为"3小时"）
    formattedTime: '3',

    // 时间单位
    // 显示用的时间单位，"小时"或"分钟"
    timeUnit: '小时',

    /**
     * 维护模式设置
     *
     * 系统维护功能：
     * 当系统需要维护时，可以开启维护模式
     * 开启后，普通用户将无法使用预约等核心功能
     * 只有管理员可以正常使用系统
     */

    // 维护模式开关
    // 布尔值，true表示系统处于维护状态
    maintenanceMode: false,

    /**
     * 联系信息设置
     *
     * 对外展示信息：
     * 这些信息会在首页、个人中心等地方展示给用户
     * 用于用户联系门店或了解相关信息
     */

    // 联系电话
    // 字符串类型，门店的联系电话号码
    contactPhone: '',

    // 联系地址
    // 字符串类型，门店的详细地址信息
    contactAddress: '',

    // 联系公告
    // 字符串类型，向用户展示的重要公告或通知
    contactAnnouncement: '',

    /**
     * UI状态管理
     *
     * 页面交互状态：
     * 控制页面的加载、保存等状态显示
     */

    // 保存状态标识
    // 布尔值，true表示正在保存设置，用于显示保存中的状态
    isSaving: false,

    /**
     * 权限控制
     *
     * 安全验证：
     * 确保只有管理员可以访问和修改系统设置
     */

    // 管理员权限标识
    // 布尔值，true表示当前用户具有管理员权限
    isAdmin: false
  },

  onLoad(options) {
    this.checkAdminPermission()
    this.loadSettings()
  },

  /**
   * formatTimeDisplay: 格式化时间显示的工具方法
   *
   * 功能说明：
   * 将分钟数转换为用户友好的显示格式
   * 自动选择合适的时间单位（小时或分钟）
   *
   * @param {number} minutes - 分钟数
   * @returns {Object} - 包含格式化时间和单位的对象
   *   - formattedTime: 格式化后的时间数字
   *   - timeUnit: 时间单位（'小时' 或 '分钟'）
   *
   * 格式化规则：
   * 1. 整小时：180分钟 → "3小时"
   * 2. 小数小时：150分钟 → "2.5小时"
   * 3. 不足1小时：30分钟 → "30分钟"
   * 4. 无效值：返回默认值"180分钟"
   *
   * 用户体验：
   * 用户更容易理解"3小时"而不是"180分钟"
   * 但对于不规整的时间，仍保持精确性
   */
  formatTimeDisplay(minutes) {
    /**
     * 输入验证和默认值处理
     *
     * 处理情况：
     * - null: 数据库中可能存储的空值
     * - undefined: 未设置的情况
     * - NaN: 非数字输入
     *
     * 默认值选择：
     * 180分钟（3小时）是常见的预约取消时间限制
     */
    if (minutes === null || minutes === undefined || isNaN(minutes)) {
      return {
        formattedTime: '180',
        timeUnit: '分钟'
      }
    }

    /**
     * 时间单位转换逻辑
     *
     * 转换规则：
     * >= 60分钟：转换为小时显示
     * < 60分钟：保持分钟显示
     */
    if (minutes >= 60) {
      // 计算小时数（可能包含小数）
      const hours = minutes / 60

      /**
       * 判断是否为整小时
       *
       * Math.floor(hours) === hours：
       * 如果小时数的整数部分等于原值，说明是整小时
       * 例如：3.0 === 3（整小时），2.5 !== 2（非整小时）
       */
      if (hours === Math.floor(hours)) {
        // 整小时显示
        return {
          formattedTime: hours.toString(),
          timeUnit: '小时'
        }
      } else {
        // 小数小时显示（保留1位小数）
        return {
          formattedTime: hours.toFixed(1),
          timeUnit: '小时'
        }
      }
    } else {
      // 不足60分钟，直接显示分钟数
      return {
        formattedTime: minutes.toString(),
        timeUnit: '分钟'
      }
    }
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadSettings()
  },

  // 检查管理员权限
  async checkAdminPermission() {
    try {
      const userInfo = wx.getStorageSync('userInfo')
      if (!userInfo || !userInfo.roles) {
        showToast(this, { message: '请先登录', theme: 'error' });
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
        return
      }

      const isAdmin = userInfo.roles.includes('管理员')
      this.setData({ isAdmin })

      if (!isAdmin) {
        showToast(this, { message: '权限不足，仅管理员可访问', theme: 'error' });
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      console.error('权限检查失败:', error)
      showToast(this, { message: '权限检查失败', theme: 'error' });
    }
  },

  /**
   * loadSettings: 加载系统设置的异步方法
   *
   * 功能说明：
   * 从云函数获取系统设置数据并更新页面显示
   * 使用云函数而不是直接查询数据库，确保数据安全性
   *
   * 数据流程：
   * 1. 显示加载提示
   * 2. 调用云函数获取设置
   * 3. 格式化数据并更新页面
   * 4. 隐藏加载提示
   * 5. 错误处理和用户反馈
   *
   * 安全考虑：
   * 通过云函数访问数据，可以在服务端进行权限验证
   * 避免客户端直接访问敏感的系统设置数据
   */
  async loadSettings() {
    try {
      // 显示加载提示，提升用户体验
      showLoading(this, '加载中...');

      /**
       * 调用云函数获取系统设置
       *
       * 云函数优势：
       * 1. 服务端权限验证：确保只有管理员可以获取设置
       * 2. 数据安全：敏感设置不直接暴露给客户端
       * 3. 业务逻辑封装：复杂的数据处理在服务端完成
       * 4. 统一接口：提供标准化的数据访问接口
       */
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',        // 云函数名称
        data: {
          action: 'getSystemSettings'   // 操作类型
        }
      })

      /**
       * 处理云函数返回结果
       *
       * 返回格式：
       * {
       *   result: {
       *     success: boolean,
       *     data: {
       *       booking: { cancelTimeLimitMinutes: number },
       *       maintenance: { enabled: boolean },
       *       contact: { phone: string, address: string, announcement: string }
       *     }
       *   }
       * }
       */
      if (result.result.success) {
        const settings = result.result.data

        /**
         * 提取和处理取消时间限制
         *
         * 可选链操作符(?.)：
         * settings.booking?.cancelTimeLimitMinutes
         * 安全地访问嵌套属性，如果booking不存在，返回undefined
         */
        const cancelTimeLimit = settings.booking?.cancelTimeLimitMinutes

        // 格式化时间显示
        const timeDisplay = this.formatTimeDisplay(cancelTimeLimit)

        /**
         * 批量更新页面数据
         *
         * 数据处理策略：
         * 1. 使用默认值：|| 操作符提供合理的默认值
         * 2. 可选链访问：?.操作符安全访问嵌套属性
         * 3. 格式化处理：时间数据经过格式化处理
         *
         * 默认值设计：
         * - cancelTimeLimit: 180分钟（3小时）
         * - maintenanceMode: false（非维护状态）
         * - 联系信息: 空字符串（用户可以填写）
         */
        this.setData({
          cancelTimeLimit: cancelTimeLimit || 180,              // 取消时间限制（分钟）
          formattedTime: timeDisplay.formattedTime,             // 格式化时间数字
          timeUnit: timeDisplay.timeUnit,                       // 时间单位
          maintenanceMode: settings.maintenance?.enabled || false, // 维护模式开关
          contactPhone: settings.contact?.phone || '',          // 联系电话
          contactAddress: settings.contact?.address || '',      // 联系地址
          contactAnnouncement: settings.contact?.announcement || '' // 联系公告
        })
      } else {
        // 云函数执行失败的处理
        showToast(this, { message: '获取系统设置失败', theme: 'error' });
      }

      // 隐藏加载提示
      hideToast(this);
    } catch (error) {
      /**
       * 异常处理
       *
       * 可能的异常：
       * 1. 网络错误：网络连接问题
       * 2. 云函数错误：服务端处理异常
       * 3. 权限错误：用户权限不足
       * 4. 数据格式错误：返回数据格式异常
       *
       * 处理策略：
       * 1. 记录错误日志：便于调试和问题排查
       * 2. 隐藏加载提示：恢复页面正常状态
       * 3. 显示错误提示：告知用户操作失败
       */
      console.error('加载设置失败:', error)
      hideToast(this);
      showToast(this, { message: '加载设置失败', theme: 'error' });
    }
  },

  // 时间输入改变
  onCancelTimeChange(e) {
    const value = e.detail.value
    const timeDisplay = this.formatTimeDisplay(value)
    this.setData({
      cancelTimeLimit: value,
      formattedTime: timeDisplay.formattedTime,
      timeUnit: timeDisplay.timeUnit
    })
  },

  // 时间输入失焦验证
  onCancelTimeBlur(e) {
    const value = parseInt(e.detail.value)
    if (isNaN(value) || value < 1 || value > 1440) {
      showToast(this, { message: '请输入1-1440之间的数字（分钟）', theme: 'error' });
      return
    }
    
    const timeDisplay = this.formatTimeDisplay(value)
    this.setData({
      cancelTimeLimit: value,
      formattedTime: timeDisplay.formattedTime,
      timeUnit: timeDisplay.timeUnit
    })
  },

  // 联系电话输入改变
  onContactPhoneChange(e) {
    this.setData({
      contactPhone: e.detail.value
    })
  },

  // 门店地址输入改变
  onContactAddressChange(e) {
    this.setData({
      contactAddress: e.detail.value
    })
  },

  // 门店公告输入改变
  onContactAnnouncementChange(e) {
    this.setData({
      contactAnnouncement: e.detail.value
    })
  },

  // 保存设置
  async saveSettings() {
    if (!this.data.isAdmin) {
      showToast(this, { message: '权限不足', theme: 'error' });
      return
    }

    try {
      this.setData({ isSaving: true })
      
      // 调用云函数保存系统设置
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'updateSystemSettings',
          data: {
            booking: {
              cancelTimeLimitMinutes: this.data.cancelTimeLimit
            },
            contact: {
              phone: this.data.contactPhone,
              address: this.data.contactAddress,
              announcement: this.data.contactAnnouncement
            }
          }
        }
      })
      
      if (result.result.success) {
        this.setData({ isSaving: false })
        showToast(this, { message: '设置保存成功', theme: 'success' });
        
        // 延迟返回，让用户看到提示
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        this.setData({ isSaving: false })
        showToast(this, { message: result.result.message || '保存设置失败', theme: 'error' });
      }
      
    } catch (error) {
      console.error('保存设置失败:', error)
      this.setData({ isSaving: false })
      showToast(this, { message: '保存设置失败', theme: 'error' });
    }
  },



  // 显示确认对话框
  showConfirmDialog(title, content, onConfirm) {
    const dialog = this.selectComponent('#t-dialog')
    dialog.setData({
      visible: true,
      title: title,
      content: content,
      confirmBtn: '确定',
      cancelBtn: '取消'
    })
    
    dialog.setData({
      onConfirm: () => {
        onConfirm()
        dialog.setData({ visible: false })
      },
      onCancel: () => {
        dialog.setData({ visible: false })
      }
    })
  },



  /**
   * showMessage: 显示消息提示的工具方法
   *
   * 功能说明：
   * 使用TDesign的Message组件显示消息提示
   * 提供统一的消息提示接口
   *
   * @param {string} message - 提示消息内容
   * @param {string} type - 消息类型，默认为'info'
   *   可选值：'info', 'success', 'warning', 'error'
   *
   * 组件特点：
   * - 自动消失：3秒后自动隐藏
   * - 主题样式：根据类型显示不同的颜色和图标
   * - 非阻塞：不会阻止用户的其他操作
   *
   * 使用场景：
   * - 操作成功提示
   * - 警告信息
   * - 错误提示
   * - 一般信息通知
   */
  showMessage(message, type = 'info') {
    // 获取Message组件实例
    const messageComponent = this.selectComponent('#t-message')

    // 显示消息提示
    messageComponent.show({
      message: message,     // 提示内容
      theme: type,          // 主题类型
      duration: 3000        // 显示时长：3秒
    })
  },

  /**
   * onPullDownRefresh: 下拉刷新事件处理
   *
   * 功能说明：
   * 用户下拉页面时触发，重新加载系统设置数据
   * 确保用户看到的是最新的设置信息
   *
   * 实现逻辑：
   * 1. 调用loadSettings()重新加载数据
   * 2. 无论成功还是失败，都要停止下拉刷新动画
   * 3. 使用Promise的then/catch处理异步结果
   *
   * 用户体验：
   * - 提供手动刷新的方式
   * - 确保数据的实时性
   * - 刷新完成后停止loading动画
   *
   * 注意事项：
   * 必须调用wx.stopPullDownRefresh()停止刷新动画
   * 否则页面会一直显示刷新状态
   */
  onPullDownRefresh() {
    // 重新加载设置数据
    this.loadSettings()
      .then(() => {
        // 加载成功，停止下拉刷新动画
        wx.stopPullDownRefresh()
      })
      .catch(() => {
        // 加载失败，也要停止下拉刷新动画
        wx.stopPullDownRefresh()
      })
  }
})

/**
 * 文件总结：system-settings.js
 *
 * 这个文件实现了一个完整的系统设置管理页面，主要特点：
 *
 * 1. 权限控制系统：
 *    - 严格的管理员权限验证
 *    - 多重安全检查机制
 *    - 权限不足时的友好提示和强制返回
 *
 * 2. 系统设置管理：
 *    - 预约取消时间限制设置
 *    - 维护模式开关控制
 *    - 联系信息管理（电话、地址、公告）
 *    - 智能的时间格式化显示
 *
 * 3. 数据安全设计：
 *    - 通过云函数访问数据，服务端权限验证
 *    - 敏感操作的二次确认机制
 *    - 完善的错误处理和用户反馈
 *
 * 4. 用户体验优化：
 *    - 智能的时间显示格式（自动选择小时/分钟）
 *    - 实时的输入验证和格式化
 *    - 加载状态提示和错误反馈
 *    - 下拉刷新支持
 *
 * 5. 技术特点：
 *    - 云函数集成：安全的数据访问方式
 *    - 组件化设计：使用TDesign组件库
 *    - 异步处理：完整的async/await异步流程
 *    - 防御性编程：完善的输入验证和错误处理
 *
 * 业务价值：
 *
 * 1. 系统管理：
 *    - 提供完整的系统配置能力
 *    - 支持业务规则的灵活调整
 *    - 维护模式支持系统升级和维护
 *
 * 2. 用户服务：
 *    - 管理对外展示的联系信息
 *    - 支持公告发布和信息更新
 *    - 提升用户服务质量
 *
 * 3. 运营支持：
 *    - 灵活的预约规则设置
 *    - 系统状态的集中管理
 *    - 运营策略的快速调整
 *
 * 与您熟悉的技术对比：
 * - 权限控制：类似于ASP.NET的角色权限系统
 * - 数据访问：类似于WCF服务的安全访问模式
 * - 配置管理：类似于.NET的配置系统
 * - 用户界面：类似于WinForms的设置对话框
 * - 数据验证：类似于数据注解验证
 *
 * 学习价值：
 * 这个文件展示了如何构建一个安全、易用的系统设置管理界面，
 * 包含了权限控制、数据安全、用户体验等多个方面的最佳实践。
 */