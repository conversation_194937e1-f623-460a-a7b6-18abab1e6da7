{"setting": {"compileHotReLoad": false, "urlCheck": false, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "bigPackageSizeSupport": true, "useIsolateContext": true}, "condition": {"miniprogram": {"list": [{"name": "pages/schedule/schedule", "pathName": "pages/schedule/schedule", "query": "", "scene": null, "launchMode": "default"}, {"name": "pages/course-management/course-management", "pathName": "pages/course-management/course-management", "query": "", "launchMode": "default", "scene": null}]}}, "description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "miniprogram-6", "libVersion": "3.8.10"}